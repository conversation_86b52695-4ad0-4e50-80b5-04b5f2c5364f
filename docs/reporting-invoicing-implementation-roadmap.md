# PasaBuy Pal Reporting, Invoicing, Expenses & Payments Tracking System
## Implementation Roadmap & Technical Specification
---

## 1. Executive Summary

This document outlines the comprehensive implementation plan for enhancing PasaBuy Pal with advanced reporting, invoicing, expense tracking, and payment management capabilities. The focus is on internal tracking and business intelligence without external payment gateway integration.

### 1.1 Objectives
- **Business Intelligence**: Comprehensive reporting and analytics dashboard
- **Enhanced Invoicing**: Build upon existing invoice system with advanced features
- **Expense Management**: New module for business expense tracking and categorization
- **Payment Tracking**: Internal payment status management and reconciliation

### 1.2 Scope
- **Internal Tracking Only**: No external payment gateway integration
- **Business Intelligence Focus**: Data visualization and reporting for decision-making
- **Workflow Enhancement**: Streamline existing business processes
- **Mobile-First Design**: Maintain responsive design patterns

---

## 2. Current State Analysis

### 2.1 Existing Invoicing Infrastructure

**✅ Already Implemented:**
- Basic invoice creation and management (`/api/invoices`)
- Enhanced invoice service with advanced features (`/lib/enhanced-invoice-service.ts`)
- Invoice status tracking (DRAFT, SENT, PAID, OVERDUE, CANCELLED)
- PDF generation capabilities (`/lib/pdf-generator.ts`)
- Invoice-order relationship management
- Daily batch invoice generation
- Invoice analytics and reporting foundation

**Database Schema (Existing):**
```sql
-- Core invoice tables already exist
Invoice {
  id, invoiceNumber, customerId, status, invoiceType, priority
  subtotal, discountAmount, taxAmount, shippingCost, total
  paymentTerms, paymentMethod, currency
  issueDate, dueDate, sentDate, paidDate, overdueDate
  approvalStatus, notes, internalNotes, customerNotes
  // ... extensive tracking fields
}

InvoiceItem {
  id, invoiceId, orderId, description, quantity
  unitPrice, discountAmount, taxAmount, totalPrice
  itemType, category, sku, notes
}

InvoicePayment {
  id, invoiceId, paymentNumber, amount, paymentMethod
  paymentDate, reference, notes, processorFee
}
```

### 2.2 Current Data Models & Relationships

**Core Entities:**
- **Orders**: 40+ fields with comprehensive tracking
- **Customers**: Enhanced with business metrics and segmentation
- **Stores**: Pricing matrix integration and configuration management
- **Pricing**: Range-based tiers with automatic calculation

**API Patterns:**
- RESTful endpoints with consistent error handling
- Advanced filtering and pagination (`/lib/query-builder.ts`)
- Enhanced services pattern (`/lib/enhanced-*-service.ts`)
- Bulk operations support
- Import/export capabilities

### 2.3 UI/UX Patterns

**Design System:**
- Mobile-first responsive design (44px touch targets)
- Card-based layouts with compact variants
- Bottom navigation with context-aware actions
- Form patterns with validation and error handling
- Filter modals and advanced search capabilities

**Component Architecture:**
- Reusable UI components (`/components/ui/`)
- Responsive grid layouts (`grid-cols-1 md:grid-cols-2`)
- Consistent spacing and typography
- Accessibility-compliant interactions

---

## 3. Implementation Modules

### 3.1 Reporting & Analytics Dashboard

**Priority:** High  
**Complexity:** Medium  
**Timeline:** 3-4 weeks  

#### 3.1.1 Dashboard Components

**Overview Dashboard (`/reports/dashboard`)**
```typescript
interface DashboardMetrics {
  // Financial Overview
  totalRevenue: number
  monthlyRevenue: number
  averageOrderValue: number
  profitMargin: number
  
  // Order Analytics
  totalOrders: number
  pendingOrders: number
  completedOrders: number
  orderGrowthRate: number
  
  // Customer Insights
  totalCustomers: number
  activeCustomers: number
  newCustomers: number
  customerRetentionRate: number
  
  // Store Performance
  topPerformingStores: StoreMetrics[]
  storeOrderDistribution: StoreDistribution[]
}
```

**Key Features:**
- Real-time metrics cards with trend indicators
- Interactive charts using Chart.js or Recharts
- Date range filtering (daily, weekly, monthly, yearly)
- Export capabilities (PDF, Excel, CSV)
- Mobile-responsive dashboard layout

#### 3.1.2 Advanced Reports

**Financial Reports:**
- Revenue analysis by time period
- Profit margin analysis by store/customer
- Pricing effectiveness reports
- Invoice aging reports

**Operational Reports:**
- Order processing time analysis
- Store performance comparison
- Customer behavior analytics
- Inventory turnover reports

#### 3.1.3 Data Visualization Components

**Chart Components:**
```typescript
// Reusable chart components
<RevenueChart data={revenueData} timeRange="monthly" />
<OrderStatusPieChart data={statusData} />
<CustomerGrowthLineChart data={growthData} />
<StorePerformanceBarChart data={storeData} />
```

### 3.2 Enhanced Invoicing System

**Priority:** Medium  
**Complexity:** Low (builds on existing)  
**Timeline:** 2-3 weeks  

#### 3.2.1 Invoice Templates & Customization

**Template Management:**
- Multiple invoice templates (Standard, Premium, Custom)
- Company branding customization (logo, colors, fonts)
- Custom field configuration
- Template preview and editing interface

**Enhanced PDF Generation:**
```typescript
interface InvoiceTemplate {
  id: string
  name: string
  logoUrl?: string
  headerText?: string
  footerText?: string
  colorScheme: {
    primary: string
    secondary: string
    accent: string
  }
  layout: 'standard' | 'modern' | 'minimal'
  customFields: CustomField[]
}
```

#### 3.2.2 Bulk Invoice Operations

**Batch Processing:**
- Bulk invoice generation by date range
- Bulk status updates (send, mark paid, etc.)
- Bulk export and printing
- Automated recurring invoice generation

#### 3.2.3 Invoice Analytics

**Performance Metrics:**
- Invoice aging analysis
- Payment collection rates
- Average payment time
- Customer payment behavior patterns

### 3.3 Expense Tracking Module

**Priority:** High  
**Complexity:** High (new module)  
**Timeline:** 4-5 weeks  

#### 3.3.1 Database Schema Extensions

**New Tables:**
```sql
-- Expense categories
ExpenseCategory {
  id: Int @id @default(autoincrement())
  name: String @unique
  description: String?
  parentCategoryId: Int? // For subcategories
  color: String? // For UI visualization
  isActive: Boolean @default(true)
  createdAt: DateTime @default(now())
  updatedAt: DateTime @updatedAt
}

-- Main expense tracking
Expense {
  id: Int @id @default(autoincrement())
  expenseNumber: String @unique
  categoryId: Int
  amount: Float
  currency: String @default("PHP")
  description: String
  expenseDate: DateTime
  paymentMethod: String? // Cash, Bank Transfer, Credit Card, etc.
  vendor: String?
  receiptUrl: String? // File upload path
  notes: String?
  tags: String[] // For flexible categorization
  
  // Business context
  storeCodeId: Int? // If expense is store-specific
  orderId: Int? // If expense is order-specific
  customerId: Int? // If expense is customer-specific
  
  // Approval workflow
  status: String @default("PENDING") // PENDING, APPROVED, REJECTED, PAID
  approvedBy: String?
  approvedAt: DateTime?
  rejectedReason: String?
  
  // Financial tracking
  taxAmount: Float @default(0.00)
  taxRate: Float @default(0.00)
  isRecurring: Boolean @default(false)
  recurringFrequency: String? // MONTHLY, QUARTERLY, YEARLY
  nextRecurringDate: DateTime?
  
  createdAt: DateTime @default(now())
  updatedAt: DateTime @updatedAt
}

-- Expense attachments (receipts, invoices)
ExpenseAttachment {
  id: Int @id @default(autoincrement())
  expenseId: Int
  filename: String
  originalName: String
  mimeType: String
  fileSize: Int
  uploadedAt: DateTime @default(now())
}
```

#### 3.3.2 Expense Management Interface

**Mobile-First Forms:**
```typescript
// Expense creation form following established patterns
interface ExpenseFormData {
  categoryId: number
  amount: number
  description: string
  expenseDate: string
  paymentMethod?: string
  vendor?: string
  storeCodeId?: number
  orderId?: number
  notes?: string
  tags: string[]
  receiptFiles: File[]
}
```

**Key Features:**
- Quick expense entry with camera integration
- Receipt photo upload and OCR processing
- Recurring expense setup
- Bulk expense import from CSV/Excel
- Expense approval workflow

#### 3.3.3 Expense Analytics

**Reporting Features:**
- Expense breakdown by category
- Monthly/quarterly expense trends
- Vendor spending analysis
- Store-specific expense tracking
- Tax reporting and summaries

### 3.4 Payment Records & Reconciliation

**Priority:** Medium  
**Complexity:** Medium  
**Timeline:** 3-4 weeks  

#### 3.4.1 Enhanced Payment Tracking

**Extended Payment Schema:**
```sql
-- Enhanced payment records (extends existing InvoicePayment)
PaymentRecord {
  id: Int @id @default(autoincrement())
  paymentNumber: String @unique
  
  // Payment details
  amount: Float
  currency: String @default("PHP")
  paymentMethod: String // Cash, Bank Transfer, GCash, etc.
  paymentDate: DateTime
  
  // References
  invoiceId: Int? // If payment is for an invoice
  customerId: Int // Always required
  orderId: Int? // If payment is for specific order
  
  // Payment tracking
  reference: String? // Bank reference, transaction ID, etc.
  status: String @default("COMPLETED") // PENDING, COMPLETED, FAILED, REFUNDED
  notes: String?
  
  // Reconciliation
  isReconciled: Boolean @default(false)
  reconciledAt: DateTime?
  reconciledBy: String?
  bankStatementRef: String?
  
  // Fees and adjustments
  processingFee: Float @default(0.00)
  adjustmentAmount: Float @default(0.00)
  adjustmentReason: String?
  
  createdAt: DateTime @default(now())
  updatedAt: DateTime @updatedAt
}

-- Bank reconciliation records
BankReconciliation {
  id: Int @id @default(autoincrement())
  reconciliationDate: DateTime
  bankAccount: String
  statementBalance: Float
  systemBalance: Float
  difference: Float
  status: String @default("PENDING") // PENDING, COMPLETED, DISCREPANCY
  notes: String?
  reconciledBy: String?
  createdAt: DateTime @default(now())
}
```

#### 3.4.2 Payment Management Interface

**Payment Dashboard:**
- Payment status overview
- Pending payments tracking
- Payment method analytics
- Customer payment history
- Reconciliation status monitoring

**Reconciliation Tools:**
- Bank statement import and matching
- Automatic payment matching algorithms
- Manual reconciliation interface
- Discrepancy reporting and resolution

---

## 4. Technical Implementation Details

### 4.1 API Endpoint Structure

Following established patterns:

```typescript
// Reporting endpoints
GET /api/reports/dashboard
GET /api/reports/financial
GET /api/reports/operational
POST /api/reports/export

// Enhanced invoicing
GET /api/enhanced/invoices
POST /api/enhanced/invoices/bulk-operations
GET /api/enhanced/invoices/templates
POST /api/enhanced/invoices/templates

// Expense management
GET /api/expenses
POST /api/expenses
PUT /api/expenses/[id]
DELETE /api/expenses/[id]
GET /api/expenses/categories
POST /api/expenses/bulk-import
GET /api/expenses/analytics

// Payment tracking
GET /api/payments
POST /api/payments
PUT /api/payments/[id]
POST /api/payments/reconcile
GET /api/payments/reconciliation-status
```

### 4.2 Service Layer Architecture

```typescript
// Following established enhanced service pattern
class ReportingService {
  static async getDashboardMetrics(filters: DateRangeFilter): Promise<DashboardMetrics>
  static async generateFinancialReport(params: ReportParams): Promise<FinancialReport>
  static async exportReport(reportType: string, format: 'pdf' | 'excel' | 'csv'): Promise<Buffer>
}

class ExpenseService {
  static async createExpense(data: ExpenseData): Promise<Expense>
  static async getExpenseAnalytics(filters: ExpenseFilters): Promise<ExpenseAnalytics>
  static async processRecurringExpenses(): Promise<void>
  static async importExpensesFromCSV(file: Buffer): Promise<ImportResult>
}

class PaymentService {
  static async recordPayment(data: PaymentData): Promise<PaymentRecord>
  static async reconcilePayments(bankData: BankStatement[]): Promise<ReconciliationResult>
  static async getPaymentAnalytics(filters: PaymentFilters): Promise<PaymentAnalytics>
}
```

### 4.3 Component Architecture

**Responsive Dashboard Layout:**
```typescript
// Dashboard page structure
<DashboardLayout>
  <DashboardHeader />
  <MetricsGrid className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
    <MetricCard title="Total Revenue" value={totalRevenue} trend={revenueTrend} />
    <MetricCard title="Active Orders" value={activeOrders} trend={orderTrend} />
    <MetricCard title="Customers" value={customerCount} trend={customerTrend} />
    <MetricCard title="Avg Order Value" value={avgOrderValue} trend={aovTrend} />
  </MetricsGrid>
  
  <ChartsGrid className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
    <RevenueChart />
    <OrderStatusChart />
    <CustomerGrowthChart />
    <StorePerformanceChart />
  </ChartsGrid>
</DashboardLayout>
```

**Mobile-First Form Patterns:**
```typescript
// Expense form following established patterns
<Form {...form}>
  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
    <FormField name="categoryId" />
    <FormField name="amount" />
    <FormField name="expenseDate" />
    <FormField name="paymentMethod" />
  </div>
  
  <div className="grid grid-cols-1 gap-4">
    <FormField name="description" />
    <FormField name="vendor" />
    <FormField name="notes" />
  </div>
  
  <ReceiptUpload onFilesChange={handleReceiptUpload} />
  
  <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
    <Button type="submit" className="flex-1 min-h-[48px]">Save Expense</Button>
    <Button type="button" variant="outline" className="flex-1 min-h-[48px]">Cancel</Button>
  </div>
</Form>
```

---

## 5. Implementation Phases

### Phase 1: Foundation & Reporting (Weeks 1-4)
**Priority:** High  
**Dependencies:** None  

**Deliverables:**
- Dashboard infrastructure setup
- Basic reporting API endpoints
- Core analytics components
- Data visualization library integration
- Mobile-responsive dashboard layout

**Technical Tasks:**
1. Set up chart library (Chart.js or Recharts)
2. Create dashboard layout components
3. Implement basic metrics calculation
4. Build responsive chart components
5. Add export functionality

### Phase 2: Enhanced Invoicing (Weeks 3-5)
**Priority:** Medium  
**Dependencies:** Phase 1 (partial)  

**Deliverables:**
- Invoice template system
- Bulk invoice operations
- Enhanced PDF generation
- Invoice analytics integration

**Technical Tasks:**
1. Extend existing invoice service
2. Create template management interface
3. Implement bulk operations API
4. Enhance PDF generation with templates
5. Add invoice analytics to dashboard

### Phase 3: Expense Tracking (Weeks 5-9)
**Priority:** High  
**Dependencies:** Phase 1  

**Deliverables:**
- Complete expense management module
- Receipt upload and processing
- Expense analytics and reporting
- Mobile-optimized expense entry

**Technical Tasks:**
1. Database schema migration for expenses
2. Expense service implementation
3. Mobile-first expense forms
4. Receipt upload and storage
5. Expense analytics integration
6. Recurring expense automation

### Phase 4: Payment Tracking (Weeks 8-11)
**Priority:** Medium  
**Dependencies:** Phase 2, Phase 3  

**Deliverables:**
- Enhanced payment tracking system
- Bank reconciliation tools
- Payment analytics
- Reconciliation dashboard

**Technical Tasks:**
1. Extend payment schema and services
2. Build reconciliation algorithms
3. Create payment management interface
4. Implement bank statement import
5. Add payment analytics to dashboard

### Phase 5: Integration & Testing (Weeks 10-12)
**Priority:** High  
**Dependencies:** All previous phases  

**Deliverables:**
- Complete system integration
- Comprehensive testing
- Performance optimization
- Documentation and training

**Technical Tasks:**
1. End-to-end integration testing
2. Performance optimization
3. Mobile responsiveness testing
4. User acceptance testing
5. Documentation completion

---

## 6. Technical Considerations

### 6.1 Database Migration Strategy

**Incremental Migrations:**
```sql
-- Migration 001: Expense categories
CREATE TABLE expense_categories (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT UNIQUE NOT NULL,
  description TEXT,
  parent_category_id INTEGER,
  color TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Migration 002: Expenses
CREATE TABLE expenses (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  expense_number TEXT UNIQUE NOT NULL,
  category_id INTEGER NOT NULL,
  amount REAL NOT NULL,
  currency TEXT DEFAULT 'PHP',
  description TEXT NOT NULL,
  expense_date DATETIME NOT NULL,
  -- ... additional fields
  FOREIGN KEY (category_id) REFERENCES expense_categories(id)
);
```

### 6.2 Performance Optimization

**Database Indexing:**
```sql
-- Critical indexes for reporting queries
CREATE INDEX idx_orders_created_at ON orders(created_at);
CREATE INDEX idx_orders_customer_date ON orders(customer_id, created_at);
CREATE INDEX idx_invoices_status_date ON invoices(status, issue_date);
CREATE INDEX idx_expenses_category_date ON expenses(category_id, expense_date);
CREATE INDEX idx_payments_date_status ON payment_records(payment_date, status);
```

**Query Optimization:**
- Implement database query caching for dashboard metrics
- Use aggregation queries for reporting
- Implement pagination for large datasets
- Add database connection pooling

### 6.3 File Storage Strategy

**Receipt and Document Storage:**
```typescript
// File upload configuration
const UPLOAD_CONFIG = {
  maxFileSize: 10 * 1024 * 1024, // 10MB
  allowedTypes: ['image/jpeg', 'image/png', 'application/pdf'],
  storageLocation: process.env.NODE_ENV === 'production' 
    ? '/var/uploads/receipts' 
    : './uploads/receipts'
}

// File processing pipeline
class FileProcessor {
  static async processReceipt(file: File): Promise<ProcessedReceipt> {
    // 1. Validate file type and size
    // 2. Generate unique filename
    // 3. Optimize image (compress, resize)
    // 4. Extract text using OCR (optional)
    // 5. Store file and return metadata
  }
}
```

### 6.4 Security Considerations

**Data Protection:**
- Implement role-based access control for sensitive financial data
- Add audit logging for all financial transactions
- Encrypt sensitive data at rest
- Implement secure file upload validation
- Add rate limiting for API endpoints

**Access Control:**
```typescript
// Role-based permissions
enum Permission {
  VIEW_REPORTS = 'view_reports',
  MANAGE_EXPENSES = 'manage_expenses',
  APPROVE_EXPENSES = 'approve_expenses',
  RECONCILE_PAYMENTS = 'reconcile_payments',
  EXPORT_DATA = 'export_data'
}

// Middleware for protected routes
const requirePermission = (permission: Permission) => {
  return (req: NextRequest, res: NextResponse, next: NextFunction) => {
    // Check user permissions
    // Allow or deny access
  }
}
```

---

## 7. Testing Strategy

### 7.1 Unit Testing
- Service layer testing with Jest
- Component testing with React Testing Library
- Database query testing with test database
- API endpoint testing with supertest

### 7.2 Integration Testing
- End-to-end workflow testing
- Database migration testing
- File upload and processing testing
- Report generation testing

### 7.3 Performance Testing
- Dashboard load time optimization
- Large dataset handling
- Concurrent user testing
- Mobile performance testing

### 7.4 User Acceptance Testing
- Mobile usability testing
- Workflow validation
- Accessibility compliance testing
- Cross-browser compatibility

---

## 8. Deployment & Maintenance

### 8.1 Deployment Strategy
- Staged rollout with feature flags
- Database migration automation
- Backup and rollback procedures
- Environment-specific configuration

### 8.2 Monitoring & Maintenance
- Application performance monitoring
- Error tracking and alerting
- Database performance monitoring
- User analytics and feedback collection

### 8.3 Documentation
- API documentation with examples
- User guides and tutorials
- Developer documentation
- Deployment and maintenance guides

---

## 9. Success Metrics

### 9.1 Technical Metrics
- Dashboard load time < 2 seconds
- Mobile responsiveness score > 95%
- API response time < 500ms
- Test coverage > 80%

### 9.2 Business Metrics
- Reduced manual data entry time by 60%
- Improved financial reporting accuracy
- Faster invoice processing
- Enhanced expense tracking compliance

### 9.3 User Experience Metrics
- Mobile usability score > 4.5/5
- Feature adoption rate > 70%
- User satisfaction score > 4.0/5
- Support ticket reduction by 40%

---

## 10. Conclusion

This implementation roadmap provides a comprehensive plan for enhancing PasaBuy Pal with advanced reporting, invoicing, expense tracking, and payment management capabilities. The phased approach ensures manageable development cycles while maintaining system stability and user experience quality.

The focus on internal tracking and business intelligence aligns with the established PasaBuy Pal business model while providing the foundation for future enhancements and integrations.

**Next Steps:**
1. Stakeholder review and approval
2. Development team assignment
3. Phase 1 implementation kickoff
4. Regular progress reviews and adjustments

---

## Appendix A: Code Examples

### A.1 Dashboard Component Implementation

```typescript
// src/app/reports/dashboard/page.tsx
'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { DateRangePicker } from '@/components/ui/date-range-picker'
import { RevenueChart } from '@/components/charts/revenue-chart'
import { OrderStatusChart } from '@/components/charts/order-status-chart'
import { MetricCard } from '@/components/dashboard/metric-card'
import { ExportButton } from '@/components/dashboard/export-button'

interface DashboardMetrics {
  totalRevenue: number
  monthlyRevenue: number
  totalOrders: number
  activeCustomers: number
  revenueGrowth: number
  orderGrowth: number
}

export default function DashboardPage() {
  const [metrics, setMetrics] = useState<DashboardMetrics | null>(null)
  const [dateRange, setDateRange] = useState({
    from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
    to: new Date()
  })
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    fetchDashboardMetrics()
  }, [dateRange])

  const fetchDashboardMetrics = async () => {
    setIsLoading(true)
    try {
      const response = await fetch(`/api/reports/dashboard?from=${dateRange.from.toISOString()}&to=${dateRange.to.toISOString()}`)
      const data = await response.json()
      setMetrics(data)
    } catch (error) {
      console.error('Error fetching dashboard metrics:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Dashboard Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold">Business Dashboard</h1>
          <p className="text-muted-foreground">Overview of your business performance</p>
        </div>
        <div className="flex flex-col sm:flex-row gap-2">
          <DateRangePicker value={dateRange} onChange={setDateRange} />
          <ExportButton onExport={handleExport} />
        </div>
      </div>

      {/* Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <MetricCard
          title="Total Revenue"
          value={metrics?.totalRevenue || 0}
          format="currency"
          trend={metrics?.revenueGrowth}
          isLoading={isLoading}
        />
        <MetricCard
          title="Monthly Revenue"
          value={metrics?.monthlyRevenue || 0}
          format="currency"
          isLoading={isLoading}
        />
        <MetricCard
          title="Total Orders"
          value={metrics?.totalOrders || 0}
          format="number"
          trend={metrics?.orderGrowth}
          isLoading={isLoading}
        />
        <MetricCard
          title="Active Customers"
          value={metrics?.activeCustomers || 0}
          format="number"
          isLoading={isLoading}
        />
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Revenue Trend</CardTitle>
          </CardHeader>
          <CardContent>
            <RevenueChart dateRange={dateRange} />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Order Status Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <OrderStatusChart dateRange={dateRange} />
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
```

### A.2 Expense Service Implementation

```typescript
// src/lib/expense-service.ts
import { prisma } from '@/lib/db'
import { Prisma } from '@prisma/client'

export interface ExpenseData {
  categoryId: number
  amount: number
  description: string
  expenseDate: Date
  paymentMethod?: string
  vendor?: string
  storeCodeId?: number
  orderId?: number
  notes?: string
  tags: string[]
  receiptFiles?: File[]
}

export interface ExpenseFilters {
  categoryIds?: number[]
  dateRange?: { from: Date; to: Date }
  amountRange?: { min: number; max: number }
  storeCodeIds?: number[]
  status?: string[]
  searchTerm?: string
}

export class ExpenseService {
  /**
   * Create a new expense record
   */
  static async createExpense(data: ExpenseData): Promise<any> {
    const expenseNumber = await this.generateExpenseNumber()

    return await prisma.expense.create({
      data: {
        expenseNumber,
        categoryId: data.categoryId,
        amount: data.amount,
        description: data.description,
        expenseDate: data.expenseDate,
        paymentMethod: data.paymentMethod,
        vendor: data.vendor,
        storeCodeId: data.storeCodeId,
        orderId: data.orderId,
        notes: data.notes,
        tags: data.tags
      },
      include: {
        category: true,
        storeCode: true,
        order: true
      }
    })
  }

  /**
   * Get expense analytics
   */
  static async getExpenseAnalytics(filters: ExpenseFilters): Promise<any> {
    const whereClause = this.buildWhereClause(filters)

    const [
      totalExpenses,
      expensesByCategory,
      monthlyTrend,
      topVendors
    ] = await Promise.all([
      // Total expenses
      prisma.expense.aggregate({
        where: whereClause,
        _sum: { amount: true },
        _count: true
      }),

      // Expenses by category
      prisma.expense.groupBy({
        by: ['categoryId'],
        where: whereClause,
        _sum: { amount: true },
        _count: true
      }),

      // Monthly trend
      prisma.$queryRaw`
        SELECT
          DATE_TRUNC('month', expense_date) as month,
          SUM(amount) as total_amount,
          COUNT(*) as expense_count
        FROM expenses
        WHERE expense_date >= ${filters.dateRange?.from || new Date(Date.now() - 365 * 24 * 60 * 60 * 1000)}
        AND expense_date <= ${filters.dateRange?.to || new Date()}
        GROUP BY DATE_TRUNC('month', expense_date)
        ORDER BY month
      `,

      // Top vendors
      prisma.expense.groupBy({
        by: ['vendor'],
        where: {
          ...whereClause,
          vendor: { not: null }
        },
        _sum: { amount: true },
        _count: true,
        orderBy: { _sum: { amount: 'desc' } },
        take: 10
      })
    ])

    return {
      totalAmount: totalExpenses._sum.amount || 0,
      totalCount: totalExpenses._count,
      expensesByCategory,
      monthlyTrend,
      topVendors
    }
  }

  /**
   * Import expenses from CSV
   */
  static async importExpensesFromCSV(fileBuffer: Buffer): Promise<any> {
    // Implementation for CSV parsing and bulk import
    // Following established patterns from enhanced-store-service.ts
    const result = {
      totalRows: 0,
      successfulRows: 0,
      failedRows: 0,
      errors: [] as any[]
    }

    // Parse CSV and validate data
    // Bulk create expenses with transaction
    // Return import results

    return result
  }

  private static async generateExpenseNumber(): Promise<string> {
    const count = await prisma.expense.count()
    return `EXP-${String(count + 1).padStart(6, '0')}`
  }

  private static buildWhereClause(filters: ExpenseFilters): Prisma.ExpenseWhereInput {
    const where: Prisma.ExpenseWhereInput = {}

    if (filters.categoryIds?.length) {
      where.categoryId = { in: filters.categoryIds }
    }

    if (filters.dateRange) {
      where.expenseDate = {
        gte: filters.dateRange.from,
        lte: filters.dateRange.to
      }
    }

    if (filters.amountRange) {
      where.amount = {
        gte: filters.amountRange.min,
        lte: filters.amountRange.max
      }
    }

    if (filters.searchTerm) {
      where.OR = [
        { description: { contains: filters.searchTerm } },
        { vendor: { contains: filters.searchTerm } },
        { notes: { contains: filters.searchTerm } }
      ]
    }

    return where
  }
}
```

### A.3 Mobile-First Expense Form

```typescript
// src/components/forms/expense-form.tsx
'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Combobox } from '@/components/ui/combobox'
import { Form, FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form'
import { ReceiptUpload } from '@/components/forms/receipt-upload'
import { TagInput } from '@/components/ui/tag-input'

const expenseSchema = z.object({
  categoryId: z.number().min(1, 'Category is required'),
  amount: z.number().min(0.01, 'Amount must be greater than 0'),
  description: z.string().min(1, 'Description is required'),
  expenseDate: z.string().min(1, 'Date is required'),
  paymentMethod: z.string().optional(),
  vendor: z.string().optional(),
  storeCodeId: z.number().optional(),
  notes: z.string().optional(),
  tags: z.array(z.string()).default([])
})

type ExpenseFormData = z.infer<typeof expenseSchema>

interface ExpenseFormProps {
  onSubmit: (data: ExpenseFormData & { receiptFiles: File[] }) => Promise<void>
  onCancel: () => void
  categories: Array<{ id: number; name: string }>
  stores: Array<{ id: number; code: string; name: string }>
  isLoading?: boolean
}

export function ExpenseForm({ onSubmit, onCancel, categories, stores, isLoading }: ExpenseFormProps) {
  const [receiptFiles, setReceiptFiles] = useState<File[]>([])

  const form = useForm<ExpenseFormData>({
    resolver: zodResolver(expenseSchema),
    defaultValues: {
      expenseDate: new Date().toISOString().split('T')[0],
      tags: []
    }
  })

  const handleSubmit = async (data: ExpenseFormData) => {
    await onSubmit({ ...data, receiptFiles })
  }

  const categoryOptions = categories.map(cat => ({
    value: cat.id.toString(),
    label: cat.name
  }))

  const storeOptions = stores.map(store => ({
    value: store.id.toString(),
    label: `${store.code} - ${store.name || 'Unnamed Store'}`
  }))

  const paymentMethodOptions = [
    { value: 'cash', label: 'Cash' },
    { value: 'bank_transfer', label: 'Bank Transfer' },
    { value: 'credit_card', label: 'Credit Card' },
    { value: 'gcash', label: 'GCash' },
    { value: 'paymaya', label: 'PayMaya' }
  ]

  return (
    <Card className="p-4">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="categoryId"
              render={({ field }) => (
                <FormItem>
                  <label className="text-sm font-medium">Category *</label>
                  <FormControl>
                    <Combobox
                      options={categoryOptions}
                      value={field.value?.toString() || ""}
                      onValueChange={(value) => field.onChange(parseInt(value))}
                      placeholder="Select category..."
                      className="min-h-[44px] text-base"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="amount"
              render={({ field }) => (
                <FormItem>
                  <label className="text-sm font-medium">Amount *</label>
                  <FormControl>
                    <div className="relative">
                      <span className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground text-base">₱</span>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        className="min-h-[44px] text-base pl-8"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="expenseDate"
              render={({ field }) => (
                <FormItem>
                  <label className="text-sm font-medium">Date *</label>
                  <FormControl>
                    <Input
                      type="date"
                      className="min-h-[44px] text-base"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="paymentMethod"
              render={({ field }) => (
                <FormItem>
                  <label className="text-sm font-medium">Payment Method</label>
                  <FormControl>
                    <Combobox
                      options={paymentMethodOptions}
                      value={field.value || ""}
                      onValueChange={field.onChange}
                      placeholder="Select payment method..."
                      className="min-h-[44px] text-base"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Description and Details */}
          <div className="grid grid-cols-1 gap-4">
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <label className="text-sm font-medium">Description *</label>
                  <FormControl>
                    <Input
                      placeholder="What was this expense for?"
                      className="min-h-[44px] text-base"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="vendor"
              render={({ field }) => (
                <FormItem>
                  <label className="text-sm font-medium">Vendor/Supplier</label>
                  <FormControl>
                    <Input
                      placeholder="Who did you pay?"
                      className="min-h-[44px] text-base"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="storeCodeId"
              render={({ field }) => (
                <FormItem>
                  <label className="text-sm font-medium">Related Store</label>
                  <FormControl>
                    <Combobox
                      options={storeOptions}
                      value={field.value?.toString() || ""}
                      onValueChange={(value) => field.onChange(value ? parseInt(value) : undefined)}
                      placeholder="Select store (optional)..."
                      className="min-h-[44px] text-base"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="tags"
              render={({ field }) => (
                <FormItem>
                  <label className="text-sm font-medium">Tags</label>
                  <FormControl>
                    <TagInput
                      value={field.value}
                      onChange={field.onChange}
                      placeholder="Add tags for better organization..."
                      className="min-h-[44px]"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <label className="text-sm font-medium">Notes</label>
                  <FormControl>
                    <Textarea
                      placeholder="Additional notes or details..."
                      className="min-h-[80px] text-base"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Receipt Upload */}
          <div>
            <label className="text-sm font-medium mb-2 block">Receipts & Documents</label>
            <ReceiptUpload
              files={receiptFiles}
              onFilesChange={setReceiptFiles}
              maxFiles={5}
              maxSize={10 * 1024 * 1024} // 10MB
            />
          </div>

          {/* Form Actions */}
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
            <Button
              type="submit"
              disabled={isLoading}
              className="flex-1 min-h-[48px] text-base font-medium order-1 sm:order-2"
            >
              {isLoading ? 'Saving...' : 'Save Expense'}
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isLoading}
              className="flex-1 min-h-[48px] text-base font-medium order-2 sm:order-1"
            >
              Cancel
            </Button>
          </div>
        </form>
      </Form>
    </Card>
  )
}
```

---

## Appendix B: Database Migration Scripts

### B.1 Expense Management Schema

```sql
-- Migration: 001_create_expense_categories.sql
CREATE TABLE expense_categories (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT UNIQUE NOT NULL,
  description TEXT,
  parent_category_id INTEGER,
  color TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (parent_category_id) REFERENCES expense_categories(id)
);

-- Insert default categories
INSERT INTO expense_categories (name, description, color) VALUES
('Office Supplies', 'Stationery, equipment, and office materials', '#3B82F6'),
('Transportation', 'Travel, fuel, and delivery costs', '#10B981'),
('Marketing', 'Advertising and promotional expenses', '#F59E0B'),
('Utilities', 'Internet, electricity, and other utilities', '#EF4444'),
('Professional Services', 'Legal, accounting, and consulting fees', '#8B5CF6'),
('Equipment', 'Hardware, software, and tools', '#06B6D4'),
('Miscellaneous', 'Other business expenses', '#6B7280');

-- Migration: 002_create_expenses.sql
CREATE TABLE expenses (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  expense_number TEXT UNIQUE NOT NULL,
  category_id INTEGER NOT NULL,
  amount REAL NOT NULL,
  currency TEXT DEFAULT 'PHP',
  description TEXT NOT NULL,
  expense_date DATETIME NOT NULL,
  payment_method TEXT,
  vendor TEXT,
  receipt_url TEXT,
  notes TEXT,
  tags TEXT, -- JSON array as text

  -- Business context
  store_code_id INTEGER,
  order_id INTEGER,
  customer_id INTEGER,

  -- Approval workflow
  status TEXT DEFAULT 'PENDING',
  approved_by TEXT,
  approved_at DATETIME,
  rejected_reason TEXT,

  -- Financial tracking
  tax_amount REAL DEFAULT 0.00,
  tax_rate REAL DEFAULT 0.00,
  is_recurring BOOLEAN DEFAULT FALSE,
  recurring_frequency TEXT,
  next_recurring_date DATETIME,

  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,

  FOREIGN KEY (category_id) REFERENCES expense_categories(id),
  FOREIGN KEY (store_code_id) REFERENCES store_codes(id),
  FOREIGN KEY (order_id) REFERENCES orders(id),
  FOREIGN KEY (customer_id) REFERENCES customers(id)
);

-- Create indexes for performance
CREATE INDEX idx_expenses_category_id ON expenses(category_id);
CREATE INDEX idx_expenses_expense_date ON expenses(expense_date);
CREATE INDEX idx_expenses_status ON expenses(status);
CREATE INDEX idx_expenses_store_code_id ON expenses(store_code_id);
CREATE INDEX idx_expenses_amount ON expenses(amount);
CREATE INDEX idx_expenses_created_at ON expenses(created_at);

-- Migration: 003_create_expense_attachments.sql
CREATE TABLE expense_attachments (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  expense_id INTEGER NOT NULL,
  filename TEXT NOT NULL,
  original_name TEXT NOT NULL,
  mime_type TEXT NOT NULL,
  file_size INTEGER NOT NULL,
  uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (expense_id) REFERENCES expenses(id) ON DELETE CASCADE
);

CREATE INDEX idx_expense_attachments_expense_id ON expense_attachments(expense_id);
```

### B.2 Enhanced Payment Tracking Schema

```sql
-- Migration: 004_create_payment_records.sql
CREATE TABLE payment_records (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  payment_number TEXT UNIQUE NOT NULL,

  -- Payment details
  amount REAL NOT NULL,
  currency TEXT DEFAULT 'PHP',
  payment_method TEXT NOT NULL,
  payment_date DATETIME NOT NULL,

  -- References
  invoice_id INTEGER,
  customer_id INTEGER NOT NULL,
  order_id INTEGER,

  -- Payment tracking
  reference TEXT,
  status TEXT DEFAULT 'COMPLETED',
  notes TEXT,

  -- Reconciliation
  is_reconciled BOOLEAN DEFAULT FALSE,
  reconciled_at DATETIME,
  reconciled_by TEXT,
  bank_statement_ref TEXT,

  -- Fees and adjustments
  processing_fee REAL DEFAULT 0.00,
  adjustment_amount REAL DEFAULT 0.00,
  adjustment_reason TEXT,

  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,

  FOREIGN KEY (invoice_id) REFERENCES invoices(id),
  FOREIGN KEY (customer_id) REFERENCES customers(id),
  FOREIGN KEY (order_id) REFERENCES orders(id)
);

-- Create indexes
CREATE INDEX idx_payment_records_payment_date ON payment_records(payment_date);
CREATE INDEX idx_payment_records_customer_id ON payment_records(customer_id);
CREATE INDEX idx_payment_records_invoice_id ON payment_records(invoice_id);
CREATE INDEX idx_payment_records_status ON payment_records(status);
CREATE INDEX idx_payment_records_is_reconciled ON payment_records(is_reconciled);

-- Migration: 005_create_bank_reconciliation.sql
CREATE TABLE bank_reconciliation (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  reconciliation_date DATETIME NOT NULL,
  bank_account TEXT NOT NULL,
  statement_balance REAL NOT NULL,
  system_balance REAL NOT NULL,
  difference REAL NOT NULL,
  status TEXT DEFAULT 'PENDING',
  notes TEXT,
  reconciled_by TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_bank_reconciliation_date ON bank_reconciliation(reconciliation_date);
CREATE INDEX idx_bank_reconciliation_status ON bank_reconciliation(status);
```

---

*This comprehensive document serves as both a technical specification and project management guide for implementing business intelligence and tracking features in the PasaBuy Pal system.*
